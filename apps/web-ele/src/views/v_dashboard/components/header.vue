<script setup lang="ts">
import type { DateDataType } from './index.d.ts';

import { reactive, ref } from 'vue';

import dayjs from 'dayjs';

import headerBg from '../assets/<EMAIL>';
import headerBgNon from '../assets/<EMAIL>';
import settings from '../assets/1465.svg';
import close from '../assets/1464.svg';
import { useRouter } from 'vue-router';
import { VbenFullScreen, VbenIconButton } from '@vben-core/shadcn-ui';
import { usePreferences } from '@vben/preferences';

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
const router = useRouter();
const openSettings = ref(false);
const preferences = usePreferences();
const handleSetBtnClick = () => {
  openSettings.value = !openSettings.value;
};

const handleCloseBtnClick = () => {
  router.back(-1);
};

const defaultTitle: string = ref('');
const dateData = reactive<DateDataType>({
  dateDay: '',
  dateYear: '',
  dateWeek: '',
  time: '',
  timing: null,
});

// const { setSettingShow } = useSettingStore();
const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
const timeFn = () => {
  dateData.timing = setInterval(() => {
    // 'YYYY-MM-DD hh : mm : ss'
    dateData.dateDay = dayjs().format('YYYY-MM-DD');
    dateData.time = dayjs().format('hh : mm : ss');
    dateData.dateWeek = weekday[dayjs().day()];
  }, 1000);
};

timeFn();
</script>

<template>
  <div class="relative box-border flex h-[90.25px] w-full">
    <slot name="left">
      <div
        class="date-time-wrap absolute left-[30px] top-[26px] flex text-[24px] font-normal leading-[34px] tracking-normal text-white opacity-100"
      >
        <div class="date mr-[30px]">{{ dateData.dateDay }}</div>
        <div class="time mr-[30px]">{{ dateData.time }}</div>
        <div class="week">{{ dateData.dateWeek }}</div>
      </div>
    </slot>
    <div
      class="absolutee left-0 right-0 top-0 mx-auto h-[90.25px] w-[1600px] bg-top bg-no-repeat opacity-100"
      :style="{
        backgroundImage: `url(${title ? headerBgNon : headerBg})`,
        backgroundSize: 'cover',
      }"
    >
      <slot v-if="title">{{ title }}</slot>
    </div>
    <slot name="right">
      <div class="absolute right-[26px] top-[26px]">
        <VbenFullScreen class="mr-6" />
        <button class="mr-6 h-[30px] w-[30px] cursor-pointer">
          <img :src="settings" alt="" class="h-full w-full" />
        </button>
        <button
          class="h-[30px] w-[30px] cursor-pointer"
          @click.stop.prevent="handleCloseBtnClick"
        >
          <img :src="close" alt="" class="h-full w-full" />
        </button>
      </div>
    </slot>
  </div>
</template>
<style scoped lang="scss">
.date-time-wrap {
  font-family: PingFang SC;
}
.top_title {
}
</style>
