<script setup lang="ts">
import type { DashboardField } from '../types/dashboard';

import icon from '../assets/<EMAIL>';
import icon1 from '../assets/<EMAIL>';
interface Props {
  fields: DashboardField[];
}

const props = defineProps<Props>();

// 根据布局需求分组字段
// 上面一行：前3个字段（电压等级、变压器容量、光伏容量）
// 下面一行：后4个字段，分为两行两列
const topRowFields = props.fields.slice(0, 3);
const bottomRowFields = props.fields.slice(3, 7);
</script>

<template>
  <div class="project-basic-info flex h-full w-full flex-col">
    <!-- 上面一行：三列布局 - 精确尺寸控制 -->
    <div class="mt-[10px] flex w-full justify-between px-5">
      <div
        v-for="field in topRowFields"
        :key="field.key"
        class="info-card h-[111px] w-[100px]"
      >
        <!-- 内容区域 - 两行文字布局 -->
        <div
          class="h-full w-full pt-[9px] text-center"
          :style="{
            background: `url(${icon}) no-repeat center center`,
            backgroundSize: 'cover',
          }"
        >
          <!-- 第一行：数值 -->
          <div class="value">
            {{ field.value }}
          </div>
          <!-- 第二行：标签 -->
          <div class="label mb-[3.33px]">
            {{ field.label }}
          </div>
        </div>
      </div>
    </div>

    <!-- 下面一行：两行两列布局 - 占用剩余空间 -->
    <div class="bottom-section mb-[14px] mt-[20px] flex min-h-0 flex-1 px-5">
      <!-- 左侧两个字段 -->
      <div class="left-column flex flex-1 flex-col pl-[26px]">
        <div
          v-for="field in bottomRowFields.slice(0, 2)"
          :key="field.key"
          class="info-card flex flex-1 items-center"
        >
          <!-- 占位图标盒子 -->
          <img :src="icon1" class="icon-placeholder h-[50px] w-[50px]" />
          <div class="flex flex-1 flex-col">
            <!-- 标签 -->
            <div class="label text-sm font-normal leading-[14px]">
              {{ field.label }}
            </div>
            <!-- 数值 -->
            <div class="value">
              {{ field.value }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧两个字段 -->
      <div class="left-column flex flex-1 flex-col pl-[26px]">
        <div
          v-for="field in bottomRowFields.slice(2, 4)"
          :key="field.key"
          class="info-card flex flex-1 items-center"
        >
          <!-- 占位图标盒子 -->
          <img :src="icon1" class="icon-placeholder h-[50px] w-[50px]" />
          <div class="flex flex-1 flex-col">
            <!-- 标签 -->
            <div class="label text-sm font-normal leading-[14px]">
              {{ field.label }}
            </div>
            <!-- 数值 -->
            <div class="value">
              {{ field.value }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.project-basic-info {
  font-family: DIN-BlackItalic;
  color: hsl(var(--foreground));
  .info-card {
  }
}
.value {
  font-family: DIN-BlackItalic;
  font-size: 18px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: -0.075em;
  color: #ff9b3f;
  /* 0.4 */
  font-family: DIN-BlackItalic;
  font-weight: 500;
  font-size: 22px;
  font-variation-settings: 'opsz' auto;

  /*   */
  font-family: DIN-BlackItalic;
  font-weight: 500;
  font-size: 28px;
  font-variation-settings: 'opsz' auto;

  /* kv */
  font-family: DIN-BlackItalic;
  font-weight: 500;
  font-size: 18px;
  font-variation-settings: 'opsz' auto;
}
.label {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #e2ebfa;
}

.bottom-section .value {
  font-family: DIN-BlackItalic;
  font-size: 18px;
  font-weight: 500;
  line-height: 18px;
  display: flex;
  align-items: center;
  letter-spacing: normal;
  color: #ffffff;
  text-shadow: 0px 0px 6.8px #5774bd;
}
</style>
